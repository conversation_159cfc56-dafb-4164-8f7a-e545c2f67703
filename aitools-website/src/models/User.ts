import mongoose, { Document, Schema } from 'mongoose';

export interface IAccount {
  provider: 'google' | 'github' | 'email';
  providerId: string;
  providerAccountId: string;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: Date;
}

export interface IUser extends Document {
  email: string;
  name: string;
  avatar?: string;
  role: 'user' | 'admin';
  isActive: boolean;

  // 认证相关
  emailVerified: boolean;
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;

  // OAuth账户关联
  accounts: IAccount[];

  // 用户行为
  submittedTools: string[]; // Tool IDs
  likedTools: string[]; // Tool IDs
  comments: string[]; // Comment IDs

  // 时间戳
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;

  // 便捷访问器
  isAdmin: boolean;
}

const AccountSchema = new Schema({
  provider: {
    type: String,
    required: true,
    enum: ['google', 'github', 'email']
  },
  providerId: {
    type: String,
    required: true
  },
  providerAccountId: {
    type: String,
    required: true
  },
  accessToken: String,
  refreshToken: String,
  expiresAt: Date
}, { _id: false });

const UserSchema: Schema = new Schema({
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    trim: true,
    lowercase: true,
    validate: {
      validator: function(v: string) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
      },
      message: 'Please enter a valid email address'
    }
  },
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  avatar: {
    type: String,
    trim: true
  },
  role: {
    type: String,
    required: true,
    enum: ['user', 'admin'],
    default: 'user'
  },
  isActive: {
    type: Boolean,
    default: true
  },

  // 认证相关
  emailVerified: {
    type: Boolean,
    default: false
  },
  emailVerificationToken: {
    type: String,
    trim: true
  },
  emailVerificationExpires: {
    type: Date
  },

  // OAuth账户关联
  accounts: [AccountSchema],

  // 用户行为
  submittedTools: [{
    type: Schema.Types.ObjectId,
    ref: 'Tool'
  }],
  likedTools: [{
    type: Schema.Types.ObjectId,
    ref: 'Tool'
  }],
  comments: [{
    type: Schema.Types.ObjectId,
    ref: 'Comment'
  }],

  // 时间戳
  lastLoginAt: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for isAdmin
UserSchema.virtual('isAdmin').get(function() {
  return this.role === 'admin';
});

// Indexes
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ emailVerificationToken: 1 });
UserSchema.index({ 'accounts.provider': 1, 'accounts.providerAccountId': 1 });

// 实例方法
UserSchema.methods.addAccount = function(account: IAccount) {
  // 检查是否已存在相同的账户
  const existingAccount = this.accounts.find(
    (acc: IAccount) => acc.provider === account.provider && acc.providerAccountId === account.providerAccountId
  );

  if (!existingAccount) {
    this.accounts.push(account);
  } else {
    // 更新现有账户信息
    Object.assign(existingAccount, account);
  }
};

UserSchema.methods.removeAccount = function(provider: string, providerAccountId: string) {
  this.accounts = this.accounts.filter(
    (acc: IAccount) => !(acc.provider === provider && acc.providerAccountId === providerAccountId)
  );
};

export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
