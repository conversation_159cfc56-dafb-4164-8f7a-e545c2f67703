import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

export default withAuth(
  function middleware(req) {
    // 如果用户已经通过了认证检查，这里可以添加额外的逻辑
    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // 检查是否访问管理页面
        if (req.nextUrl.pathname.startsWith('/admin')) {
          // 管理页面需要管理员权限
          return token?.role === 'admin' || token?.isAdmin === true;
        }
        
        // 其他受保护的页面只需要登录
        return !!token;
      },
    },
  }
);

// 配置需要保护的路径
export const config = {
  matcher: [
    '/admin/:path*',
    '/profile/:path*',
    '/submit/:path*',
  ]
};
