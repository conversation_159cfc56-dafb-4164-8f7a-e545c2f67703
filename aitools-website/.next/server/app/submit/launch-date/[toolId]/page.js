(()=>{var e={};e.id=8146,e.ids=[8146],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11945:(e,t,s)=>{Promise.resolve().then(s.bind(s,82962))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21673:(e,t,s)=>{Promise.resolve().then(s.bind(s,57792))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38090:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var r=s(65239),a=s(48088),o=s(88170),n=s.n(o),i=s(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d={children:["",{children:["submit",{children:["launch-date",{children:["[toolId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,57792)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/submit/launch-date/[toolId]/page",pathname:"/submit/launch-date/[toolId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},57792:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/submit/launch-date/[toolId]/page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},82962:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>p});var r=s(60687),a=s(43210),o=s(16189),n=s(82136),i=s(98402),l=s(32498),d=s(93613),c=s(5336);function p(){let e=(0,o.useParams)(),t=(0,o.useRouter)(),{status:s}=(0,n.useSession)(),[p,u]=(0,a.useState)(!1),[x,m]=(0,a.useState)(""),[h,b]=(0,a.useState)(null),[g,w]=(0,a.useState)(!0),f=e.toolId,v=async(e,s)=>{u(!0),m("");try{let r=await fetch(`/api/tools/${f}/launch-date`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({launchOption:e,selectedDate:s})}),a=await r.json();a.success?"paid"===e&&a.data.paymentUrl?window.location.href=a.data.paymentUrl:t.push(`/submit/success?toolId=${f}`):m(a.message||"提交失败")}catch{m("网络错误，请重试")}finally{u(!1)}};return g?(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"加载中..."})]})})}):x?(0,r.jsx)(i.A,{children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(d.A,{className:"h-12 w-12 text-red-500 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"出错了"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:x}),(0,r.jsx)("button",{onClick:()=>t.push("/submit"),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"返回提交页面"})]})})}):(0,r.jsx)(i.A,{children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsx)(c.A,{className:"h-12 w-12 text-green-500 mx-auto mb-4"}),(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"工具信息提交成功！"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"现在请选择您的发布日期和选项"})]}),h&&(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:h.name}),(0,r.jsx)("p",{className:"text-gray-600",children:h.description})]}),h&&(0,r.jsx)(l.A,{toolId:f,onSubmit:v,isSubmitting:p,error:x})]})})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,865,1658,6707,8162],()=>s(38090));module.exports=r})();