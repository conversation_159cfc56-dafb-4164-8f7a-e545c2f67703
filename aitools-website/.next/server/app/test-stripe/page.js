(()=>{var e={};e.id=311,e.ids=[311],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3998:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let c={children:["",{children:["test-stripe",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,52693)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-stripe/page",pathname:"/test-stripe",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24303:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),a=r(43210),i=r(39010),l=r(46299),n=r(94865);let o=(0,i.c)("pk_test_51K8G6vHZxvPjnxC8Jw3r7UbsBk8bdoy8txs1BJBaL8bPUM04LapfMJZAoK30RMqjHIuF1ANtm3nIjx5QGdWWym3J00v6hTFdQM");function c(){let e=(0,l.t2)(),t=(0,l.HH)(),[r,i]=(0,a.useState)(!1),[o,c]=(0,a.useState)(""),d=async r=>{if(r.preventDefault(),!e||!t)return;i(!0);let{error:s}=await e.confirmPayment({elements:t,confirmParams:{return_url:`${window.location.origin}/test-stripe`},redirect:"if_required"});s?c(s.message||"支付失败"):c("支付成功！"),i(!1)};return(0,s.jsxs)("form",{onSubmit:d,className:"space-y-4",children:[(0,s.jsx)(l.He,{}),(0,s.jsx)("button",{disabled:!e||!t||r,className:"w-full bg-blue-600 text-white py-2 px-4 rounded disabled:opacity-50",children:r?"处理中...":`支付 ${(0,n.$g)(n.kX.PRIORITY_LAUNCH.displayPrice)}`}),o&&(0,s.jsx)("div",{className:"text-center text-sm",children:o})]})}function d(){let[e,t]=(0,a.useState)(""),[r,i]=(0,a.useState)(!1),d=async()=>{i(!0);try{let e=await fetch("/api/test/create-payment-intent",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:n.kX.PRIORITY_LAUNCH.stripeAmount,currency:"cny"})}),r=await e.json();r.success?t(r.clientSecret):alert("创建支付失败: "+r.message)}catch(e){alert("创建支付失败")}finally{i(!1)}};return(0,s.jsxs)("div",{className:"max-w-md mx-auto mt-8 p-6 bg-white rounded-lg shadow-lg",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Stripe 支付测试"}),e?(0,s.jsx)(l.S8,{stripe:o,options:{clientSecret:e,appearance:{theme:"stripe"}},children:(0,s.jsx)(c,{})}):(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("button",{onClick:d,disabled:r,className:"bg-green-600 text-white py-2 px-4 rounded disabled:opacity-50",children:r?"创建中...":"创建测试支付"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 mt-2",children:["点击创建一个 ",(0,n.$g)(n.kX.PRIORITY_LAUNCH.displayPrice)," 的测试支付"]})]}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-gray-100 rounded text-sm",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"测试卡号:"}),(0,s.jsxs)("ul",{className:"space-y-1 text-gray-600",children:[(0,s.jsx)("li",{children:"成功: 4242 4242 4242 4242"}),(0,s.jsx)("li",{children:"失败: 4000 0000 0000 0002"}),(0,s.jsx)("li",{children:"需要验证: 4000 0025 0000 3155"})]}),(0,s.jsx)("p",{className:"mt-2 text-xs",children:"使用任意未来日期作为过期时间，任意3位数作为CVC"})]})]})}},25430:(e,t,r)=>{Promise.resolve().then(r.bind(r,52693))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52693:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/test-stripe/page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72286:(e,t,r)=>{Promise.resolve().then(r.bind(r,24303))},79551:e=>{"use strict";e.exports=require("url")},94865:(e,t,r)=>{"use strict";r.d(t,{$g:()=>d,Ef:()=>o,Y$:()=>n,kX:()=>s,mV:()=>c,tF:()=>u,v4:()=>l,vS:()=>a});let s={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},a=[{id:"free",title:"免费发布",description:s.FREE_LAUNCH.description,price:s.FREE_LAUNCH.displayPrice,features:s.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:s.PRIORITY_LAUNCH.description,price:s.PRIORITY_LAUNCH.displayPrice,features:s.PRIORITY_LAUNCH.features,recommended:!0}],i={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},l=[{value:"",label:"所有价格"},{value:i.FREE.value,label:i.FREE.label},{value:i.FREEMIUM.value,label:i.FREEMIUM.label},{value:i.PAID.value,label:i.PAID.label}],n=[{value:i.FREE.value,label:i.FREE.label},{value:i.FREEMIUM.value,label:i.FREEMIUM.label},{value:i.PAID.value,label:i.PAID.label}],o=e=>{switch(e){case i.FREE.value:return i.FREE.color;case i.FREEMIUM.value:return i.FREEMIUM.color;case i.PAID.value:return i.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case i.FREE.value:return i.FREE.label;case i.FREEMIUM.value:return i.FREEMIUM.label;case i.PAID.value:return i.PAID.label;default:return e}},d=e=>0===e?"免费":`\xa5${e}`,u=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,865,1658,7580,6707],()=>r(3998));module.exports=s})();