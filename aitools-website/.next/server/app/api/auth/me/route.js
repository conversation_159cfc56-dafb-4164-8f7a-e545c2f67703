(()=>{var e={};e.id=8673,e.ids=[8673],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,r,t)=>{"use strict";t.d(r,{N:()=>c});var i=t(36344),s=t(65752),o=t(13581),a=t(75745),n=t(17063);let c={providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,o.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,a.A)();let r=await n.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!r)return null;let t=r.emailVerificationToken;if(!t||!t.includes(":"))return null;let[i,s]=t.split(":");if(i!==e.token||s!==e.code)return null;return r.emailVerified=!0,r.emailVerificationToken=void 0,r.emailVerificationExpires=void 0,r.lastLoginAt=new Date,r.accounts.some(e=>"email"===e.provider)||r.accounts.push({provider:"email",providerId:"email",providerAccountId:r.email}),await r.save(),{id:r._id.toString(),email:r.email,name:r.name,image:r.avatar,role:r.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:r,profile:t}){if(r?.provider==="email-code")return!0;await (0,a.A)();try{let i=await n.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new n.A({email:e.email,name:e.name||t?.name||"User",avatar:e.image||t?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),r&&"email-code"!==r.provider&&(i.addAccount({provider:r.provider,providerId:r.provider,providerAccountId:r.providerAccountId||r.id||"",accessToken:r.access_token,refreshToken:r.refresh_token,expiresAt:r.expires_at?new Date(1e3*r.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:r})=>(r&&(e.userId=r.id,e.role=r.role||"user"),e),session:async({session:e,token:r})=>(r&&e.user&&(e.user.id=r.userId,e.user.role=r.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},16194:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>v,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var i={};t.r(i),t.d(i,{GET:()=>p,PUT:()=>m});var s=t(96559),o=t(48088),a=t(37719),n=t(32190),c=t(35426),u=t(12909),l=t(75745),d=t(17063);async function p(e){try{let e=await (0,c.getServerSession)(u.N);if(!e?.user?.email)return n.NextResponse.json({success:!1,error:"未登录"},{status:401});await (0,l.A)();let r=await d.A.findOne({email:e.user.email}).select("-emailVerificationToken -emailVerificationExpires").populate("submittedTools","name status").populate("likedTools","name").populate("comments");if(!r)return n.NextResponse.json({success:!1,error:"用户不存在"},{status:404});return n.NextResponse.json({success:!0,data:{id:r._id.toString(),email:r.email,name:r.name,avatar:r.avatar,role:r.role,emailVerified:r.emailVerified,isActive:r.isActive,submittedTools:r.submittedTools,likedTools:r.likedTools,comments:r.comments,accounts:r.accounts.map(e=>({provider:e.provider,providerId:e.providerId})),createdAt:r.createdAt,lastLoginAt:r.lastLoginAt}})}catch(e){return console.error("Get user info error:",e),n.NextResponse.json({success:!1,error:"服务器错误，请稍后重试"},{status:500})}}async function m(e){try{let r=await (0,c.getServerSession)(u.N);if(!r?.user?.email)return n.NextResponse.json({success:!1,error:"未登录"},{status:401});await (0,l.A)();let{name:t,avatar:i}=await e.json();if(t&&("string"!=typeof t||0===t.trim().length||t.length>100))return n.NextResponse.json({success:!1,error:"用户名格式不正确"},{status:400});if(i&&"string"!=typeof i)return n.NextResponse.json({success:!1,error:"头像URL格式不正确"},{status:400});let s={};void 0!==t&&(s.name=t.trim()),void 0!==i&&(s.avatar=i);let o=await d.A.findOneAndUpdate({email:r.user.email},s,{new:!0,runValidators:!0}).select("-emailVerificationToken -emailVerificationExpires");if(!o)return n.NextResponse.json({success:!1,error:"用户不存在"},{status:404});return n.NextResponse.json({success:!0,message:"用户信息更新成功",data:{id:o._id.toString(),email:o.email,name:o.name,avatar:o.avatar,role:o.role,emailVerified:o.emailVerified}})}catch(e){return console.error("Update user info error:",e),n.NextResponse.json({success:!1,error:"服务器错误，请稍后重试"},{status:500})}}let v=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/me/route",pathname:"/api/auth/me",filename:"route",bundlePath:"app/api/auth/me/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:x}=v;function h(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},17063:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var i=t(56037),s=t.n(i);let o=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),a=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[o],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});a.index({email:1}),a.index({role:1}),a.index({emailVerificationToken:1}),a.index({"accounts.provider":1,"accounts.providerAccountId":1}),a.methods.addAccount=function(e){let r=this.accounts.find(r=>r.provider===e.provider&&r.providerAccountId===e.providerAccountId);r?Object.assign(r,e):this.accounts.push(e)},a.methods.removeAccount=function(e,r){this.accounts=this.accounts.filter(t=>t.provider!==e||t.providerAccountId!==r)};let n=s().models.User||s().model("User",a)},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var i=t(56037),s=t.n(i);let o=process.env.MONGODB_URI;if(!o)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let a=global.mongoose;a||(a=global.mongoose={conn:null,promise:null});let n=async function(){if(a.conn)return a.conn;a.promise||(a.promise=s().connect(o,{bufferCommands:!1}).then(e=>e));try{a.conn=await a.promise}catch(e){throw a.promise=null,e}return a.conn}},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[4243,580,4999,3136],()=>t(16194));module.exports=i})();