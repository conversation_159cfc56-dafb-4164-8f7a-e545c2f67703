"use strict";exports.id=635,exports.ids=[635],exports.modules={5973:(e,t,a)=>{function s(){return null}a.d(t,{default:()=>s}),a(43210)},7485:(e,t,a)=>{a.d(t,{A:()=>n});var s=a(60687),l=a(43210),r=a(82136),i=a(23877);function n({toolId:e,initialLikes:t=0,initialLiked:a=!1,onLoginRequired:n,onUnlike:o,isInLikedPage:c=!1}){let{data:d}=(0,r.useSession)(),[u,m]=(0,l.useState)(a),[x,p]=(0,l.useState)(t),[g,h]=(0,l.useState)(!1),b=async()=>{if(!d)return void n?.();if(!g){h(!0);try{let t=await fetch(`/api/tools/${e}/like`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(c?{forceUnlike:!0}:{})});if(t.ok){let a=await t.json();if(a.success){let t=a.data.liked;m(t),p(a.data.likes),!t&&o&&o(e)}}else{let e=await t.json();console.error("Like failed:",e.message)}}catch(e){console.error("Like request failed:",e)}finally{h(!1)}}};return(0,s.jsxs)("button",{onClick:b,disabled:g,className:`
        flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200
        ${u?"bg-red-50 text-red-600 hover:bg-red-100":"bg-gray-50 text-gray-600 hover:bg-gray-100"}
        ${g?"opacity-50 cursor-not-allowed":"hover:scale-105"}
        border border-gray-200 hover:border-gray-300
      `,children:[u?(0,s.jsx)(i.Mbv,{className:"w-4 h-4 text-red-500"}):(0,s.jsx)(i.sOK,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:x>0?x:""})]})}},70440:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var s=a(31658);let l=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},73899:(e,t,a)=>{a.d(t,{default:()=>h});var s=a(60687),l=a(43210),r=a(85814),i=a.n(r),n=a(25334),o=a(13861),c=a(67760),d=a(7485),u=a(30474);function m({src:e,alt:t,width:a,height:r,className:i="",priority:n=!1,fill:o=!1,sizes:c,placeholder:d="empty",blurDataURL:m,fallbackSrc:x="/images/placeholder.svg",onError:p}){let[g,h]=(0,l.useState)(e),[b,f]=(0,l.useState)(!0),[v,y]=(0,l.useState)(!1),E={src:g,alt:t,className:`${i} ${b?"opacity-0":"opacity-100"} transition-opacity duration-300`,onError:()=>{y(!0),f(!1),h(x),p?.()},onLoad:()=>{f(!1)},priority:n,placeholder:"blur"===d?"blur":"empty",blurDataURL:m||("blur"===d?((e=10,t=10)=>{let a=document.createElement("canvas");a.width=e,a.height=t;let s=a.getContext("2d");return s&&(s.fillStyle="#f3f4f6",s.fillRect(0,0,e,t)),a.toDataURL()})():void 0),sizes:c||(o?"100vw":void 0)};return o?(0,s.jsxs)("div",{className:"relative overflow-hidden",children:[(0,s.jsx)(u.default,{...E,fill:!0,style:{objectFit:"cover"}}),b&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"})]}):(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(u.default,{...E,width:a,height:r}),b&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse",style:{width:a,height:r}})]})}let x={toolLogo:{width:64,height:64}},p={toolLogo:"64px"};var g=a(94865);let h=({tool:e,onLoginRequired:t,onUnlike:a,isInLikedPage:l=!1})=>(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,s.jsx)(m,{src:e.logo,alt:`${e.name} logo`,width:x.toolLogo.width,height:x.toolLogo.height,className:"rounded-lg object-cover",sizes:p.toolLogo,placeholder:"blur"}):(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0).toUpperCase()})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.name}),(0,s.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${(0,g.Ef)(e.pricing)}`,children:(0,g.mV)(e.pricing)})]})]}),(0,s.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,s.jsx)(n.A,{className:"h-5 w-5"})})]}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:e.description}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.tags.slice(0,3).map((e,t)=>(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},t)),e.tags.length>3&&(0,s.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",e.tags.length-3]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.views})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:e.likes})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(d.A,{toolId:e._id,initialLikes:e.likes,onLoginRequired:t,onUnlike:a,isInLikedPage:l}),(0,s.jsx)(i(),{href:`/tools/${e._id}`,className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:"查看详情"})]})]})]})})},94865:(e,t,a)=>{a.d(t,{$g:()=>d,Ef:()=>o,Y$:()=>n,kX:()=>s,mV:()=>c,tF:()=>u,v4:()=>i,vS:()=>l});let s={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},l=[{id:"free",title:"免费发布",description:s.FREE_LAUNCH.description,price:s.FREE_LAUNCH.displayPrice,features:s.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:s.PRIORITY_LAUNCH.description,price:s.PRIORITY_LAUNCH.displayPrice,features:s.PRIORITY_LAUNCH.features,recommended:!0}],r={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"所有价格"},{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],n=[{value:r.FREE.value,label:r.FREE.label},{value:r.FREEMIUM.value,label:r.FREEMIUM.label},{value:r.PAID.value,label:r.PAID.label}],o=e=>{switch(e){case r.FREE.value:return r.FREE.color;case r.FREEMIUM.value:return r.FREEMIUM.color;case r.PAID.value:return r.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case r.FREE.value:return r.FREE.label;case r.FREEMIUM.value:return r.FREEMIUM.label;case r.PAID.value:return r.PAID.label;default:return e}},d=e=>0===e?"免费":`\xa5${e}`,u=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}};