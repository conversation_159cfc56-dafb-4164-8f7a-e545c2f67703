(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2868],{1544:(e,s,a)=>{Promise.resolve().then(a.bind(a,5714))},3332:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},5695:(e,s,a)=>{"use strict";var t=a(8999);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},5714:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>A});var t=a(5155),r=a(2115),l=a(5695),i=a(4478),c=a(646),d=a(4186),n=a(4861),m=a(7550),x=a(5868),o=a(9946);let h=(0,o.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var u=a(3786);let g=(0,o.A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);var b=a(3332),p=a(1007);let j=(0,o.A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);var y=a(9074),N=a(1243);let f={id:"1",name:"AI写作助手",description:"基于GPT技术的智能写作工具，支持多种文体创作，提供实时语法检查和内容优化建议。这是一个功能强大的AI写作助手，能够帮助用户快速生成高质量的文章、报告、邮件等各种文本内容。工具采用最新的自然语言处理技术，具有强大的语言理解和生成能力。",website:"https://aiwriter.example.com",logo:"https://via.placeholder.com/128",category:"text-generation",pricing:"freemium",tags:["写作","GPT","内容创作","语法检查","文本生成"],features:["智能写作：基于AI的内容生成","语法检查：实时检测和修正语法错误","多语言支持：支持中文、英文等多种语言","模板库：提供丰富的写作模板","协作功能：支持团队协作编辑","导出功能：支持多种格式导出"],submitterName:"张三",submitterEmail:"<EMAIL>",submittedAt:"2024-06-25T10:30:00Z",status:"pending",publishDate:"2024-06-28",screenshots:["https://via.placeholder.com/600x400","https://via.placeholder.com/600x400","https://via.placeholder.com/600x400"]},v={"text-generation":"文本生成","image-generation":"图像生成","code-generation":"代码生成","data-analysis":"数据分析","audio-processing":"音频处理","video-editing":"视频编辑",translation:"语言翻译","search-engines":"搜索引擎",education:"教育学习",marketing:"营销工具",productivity:"生产力工具","customer-service":"客户服务"},w={free:"免费",freemium:"免费增值",paid:"付费"};function A(){(0,l.useParams)();let e=(0,l.useRouter)(),[s]=(0,r.useState)(f),[a,o]=(0,r.useState)(!1),[A,k]=(0,r.useState)(""),[D,C]=(0,r.useState)(!1),P=async()=>{C(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("工具已批准！"),e.push("/admin")}catch(e){alert("操作失败，请重试")}finally{C(!1)}},z=async()=>{if(A.trim()){C(!0);try{await new Promise(e=>setTimeout(e,1e3)),alert("工具已拒绝！"),e.push("/admin")}catch(e){alert("操作失败，请重试")}finally{C(!1),o(!1),k("")}}};return(0,t.jsx)(i.A,{children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsxs)("button",{onClick:()=>e.back(),className:"flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors",children:[(0,t.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"返回审核列表"]}),(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,t.jsx)("img",{src:s.logo,alt:s.name,className:"w-24 h-24 rounded-xl object-cover border border-gray-200 shadow-sm"}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:s.name}),(e=>{if("approved"===e.status&&e.launchDate&&new Date(e.launchDate)<=new Date)return(0,t.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"已发布"]});switch(e.status){case"pending":return(0,t.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800",children:[(0,t.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"待审核"]});case"approved":return(0,t.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"已通过审核"]});case"rejected":return(0,t.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800",children:[(0,t.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"已拒绝"]});default:return null}})(s)]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-600 mb-4",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:v[s.category]}),(0,t.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800",children:[(0,t.jsx)(x.A,{className:"w-3 h-3 mr-1"}),w[s.pricing]]}),(0,t.jsxs)("a",{href:s.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:[(0,t.jsx)(h,{className:"w-4 h-4 mr-1"}),"访问网站",(0,t.jsx)(u.A,{className:"w-3 h-3 ml-1"})]})]}),(0,t.jsx)("p",{className:"text-gray-600 max-w-3xl",children:s.description})]})]}),"pending"===s.status&&(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsxs)("button",{onClick:P,disabled:D,className:"px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),D?"处理中...":"批准"]}),(0,t.jsxs)("button",{onClick:()=>o(!0),disabled:D,className:"px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center",children:[(0,t.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"拒绝"]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"主要功能"}),(0,t.jsx)("ul",{className:"space-y-3",children:s.features.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)(g,{className:"w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{className:"text-gray-700",children:e})]},s))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"标签"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:s.tags.map((e,s)=>(0,t.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[(0,t.jsx)(b.A,{className:"w-3 h-3 mr-1"}),e]},s))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"截图预览"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:s.screenshots.map((e,a)=>(0,t.jsx)("img",{src:e,alt:"".concat(s.name," 截图 ").concat(a+1),className:"w-full h-48 object-cover rounded-lg border border-gray-200"},a))})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"提交信息"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:s.submitterName}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"提交者"})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(j,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:s.submitterEmail}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"联系邮箱"})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(y.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(s.submittedAt).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"提交时间"})]})]}),s.selectedLaunchDate&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(s.selectedLaunchDate).toLocaleDateString("zh-CN")}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"选择的发布日期"})]})]}),s.launchDate&&(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(c.A,{className:"w-5 h-5 text-gray-400 mr-3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:new Date(s.launchDate).toLocaleDateString("zh-CN")}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"实际发布日期"})]})]})]})]}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)(N.A,{className:"w-5 h-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"审核指南"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,t.jsx)("li",{children:"• 验证工具网站是否可正常访问"}),(0,t.jsx)("li",{children:"• 检查工具描述是否准确客观"}),(0,t.jsx)("li",{children:"• 确认分类和标签是否合适"}),(0,t.jsx)("li",{children:"• 评估工具质量和实用性"}),(0,t.jsx)("li",{children:"• 检查是否存在重复提交"})]})]})]})})]})]}),a&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg max-w-md w-full p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"拒绝工具"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"请详细说明拒绝的原因，这将帮助提交者了解问题并改进他们的提交。"}),(0,t.jsx)("textarea",{value:A,onChange:e=>k(e.target.value),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入详细的拒绝原因..."}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,t.jsx)("button",{onClick:()=>{o(!1),k("")},disabled:D,className:"px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors",children:"取消"}),(0,t.jsx)("button",{onClick:z,disabled:!A.trim()||D,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:D?"处理中...":"确认拒绝"})]})]})})]})})}},5868:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7550:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,9448,8441,1684,7358],()=>s(1544)),_N_E=e.O()}]);