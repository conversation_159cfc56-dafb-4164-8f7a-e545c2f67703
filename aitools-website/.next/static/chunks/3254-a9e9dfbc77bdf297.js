"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3254],{365:(e,t,a)=>{a.d(t,{default:()=>s});var r=a(2115);function s(){return(0,r.useEffect)(()=>{let e={},t=(e,t)=>{window.gtag&&window.gtag("event","web_vitals",{event_category:"Performance",event_label:e,value:Math.round(t),non_interaction:!0})};if("undefined"!=typeof PerformanceObserver){let a;new PerformanceObserver(a=>{let r=a.getEntries().find(e=>"first-contentful-paint"===e.name);r&&(e.fcp=r.startTime,t("FCP",r.startTime))}).observe({entryTypes:["paint"]}),new PerformanceObserver(a=>{let r=a.getEntries(),s=r[r.length-1];e.lcp=s.startTime,t("LCP",s.startTime)}).observe({entryTypes:["largest-contentful-paint"]}),new PerformanceObserver(a=>{a.getEntries().forEach(a=>{e.fid=a.processingStart-a.startTime,t("FID",a.processingStart-a.startTime)})}).observe({entryTypes:["first-input"]}),a=0,new PerformanceObserver(r=>{r.getEntries().forEach(e=>{e.hadRecentInput||(a+=e.value)}),e.cls=a,t("CLS",a)}).observe({entryTypes:["layout-shift"]})}let a=performance.getEntriesByType("navigation")[0];if(a){let r=a.responseStart-a.requestStart;e.ttfb=r,t("TTFB",r)}let r=()=>{Object.keys(e).length>0&&console.log("Final Performance Metrics:",e)};return window.addEventListener("beforeunload",r),()=>{window.removeEventListener("beforeunload",r)}},[]),null}},3467:(e,t,a)=>{a.d(t,{$g:()=>d,Ef:()=>o,Y$:()=>i,kX:()=>r,mV:()=>c,tF:()=>u,v4:()=>n,vS:()=>s});let r={PRIORITY_LAUNCH:{displayPrice:19.9,stripeAmount:1990,currency:"USD",stripeCurrency:"usd",productName:"AI工具优先发布服务",description:"让您的AI工具获得优先审核和推荐位置",features:["可选择任意发布日期","优先审核处理","首页推荐位置","专属客服支持"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"免费发布服务",description:"选择一个月后的任意发布日期",features:["免费提交审核","发布日期：一个月后起","正常审核流程","标准展示位置"]}},s=[{id:"free",title:"免费发布",description:r.FREE_LAUNCH.description,price:r.FREE_LAUNCH.displayPrice,features:r.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:r.PRIORITY_LAUNCH.description,price:r.PRIORITY_LAUNCH.displayPrice,features:r.PRIORITY_LAUNCH.features,recommended:!0}],l={FREE:{value:"free",label:"免费",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:"免费增值",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"付费",color:"bg-orange-100 text-orange-800"}},n=[{value:"",label:"所有价格"},{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],i=[{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],o=e=>{switch(e){case l.FREE.value:return l.FREE.color;case l.FREEMIUM.value:return l.FREEMIUM.color;case l.PAID.value:return l.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case l.FREE.value:return l.FREE.label;case l.FREEMIUM.value:return l.FREEMIUM.label;case l.PAID.value:return l.PAID.label;default:return e}},d=e=>0===e?"免费":"\xa5".concat(e),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},4601:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(5155),s=a(2115),l=a(2108),n=a(9911);function i(e){let{toolId:t,initialLikes:a=0,initialLiked:i=!1,onLoginRequired:o,onUnlike:c,isInLikedPage:d=!1}=e,{data:u}=(0,l.useSession)(),[m,f]=(0,s.useState)(i),[g,p]=(0,s.useState)(a),[h,x]=(0,s.useState)(!1);(0,s.useEffect)(()=>{let e=async()=>{try{let e=await fetch("/api/tools/".concat(t,"/like"));if(e.ok){let t=await e.json();t.success&&(f(t.data.liked),p(t.data.likes))}}catch(e){console.error("Failed to fetch like status:",e)}};u&&e()},[t,u]);let b=async()=>{if(!u){null==o||o();return}if(!h){x(!0);try{let e=await fetch("/api/tools/".concat(t,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(d?{forceUnlike:!0}:{})});if(e.ok){let a=await e.json();if(a.success){let e=a.data.liked;f(e),p(a.data.likes),!e&&c&&c(t)}}else{let t=await e.json();console.error("Like failed:",t.message)}}catch(e){console.error("Like request failed:",e)}finally{x(!1)}}};return(0,r.jsxs)("button",{onClick:b,disabled:h,className:"\n        flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200\n        ".concat(m?"bg-red-50 text-red-600 hover:bg-red-100":"bg-gray-50 text-gray-600 hover:bg-gray-100","\n        ").concat(h?"opacity-50 cursor-not-allowed":"hover:scale-105","\n        border border-gray-200 hover:border-gray-300\n      "),children:[m?(0,r.jsx)(n.Mbv,{className:"w-4 h-4 text-red-500"}):(0,r.jsx)(n.sOK,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm font-medium",children:g>0?g:""})]})}},7797:(e,t,a)=>{a.d(t,{default:()=>h});var r=a(5155),s=a(2115),l=a(6874),n=a.n(l),i=a(3786),o=a(2657),c=a(1976),d=a(4601),u=a(6766);function m(e){let{src:t,alt:a,width:l,height:n,className:i="",priority:o=!1,fill:c=!1,sizes:d,placeholder:m="empty",blurDataURL:f,fallbackSrc:g="/images/placeholder.svg",onError:p}=e,[h,x]=(0,s.useState)(t),[b,v]=(0,s.useState)(!0),[y,E]=(0,s.useState)(!1),j={src:h,alt:a,className:"".concat(i," ").concat(b?"opacity-0":"opacity-100"," transition-opacity duration-300"),onError:()=>{E(!0),v(!1),x(g),null==p||p()},onLoad:()=>{v(!1)},priority:o,placeholder:"blur"===m?"blur":"empty",blurDataURL:f||("blur"===m?function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=document.createElement("canvas");a.width=e,a.height=t;let r=a.getContext("2d");return r&&(r.fillStyle="#f3f4f6",r.fillRect(0,0,e,t)),a.toDataURL()}():void 0),sizes:d||(c?"100vw":void 0)};return c?(0,r.jsxs)("div",{className:"relative overflow-hidden",children:[(0,r.jsx)(u.default,{...j,fill:!0,style:{objectFit:"cover"}}),b&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"})]}):(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(u.default,{...j,width:l,height:n}),b&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse",style:{width:l,height:n}})]})}let f={toolLogo:{width:64,height:64}},g={toolLogo:"64px"};var p=a(3467);let h=e=>{let{tool:t,onLoginRequired:a,onUnlike:s,isInLikedPage:l=!1}=e;return(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[t.logo?(0,r.jsx)(m,{src:t.logo,alt:"".concat(t.name," logo"),width:f.toolLogo.width,height:f.toolLogo.height,className:"rounded-lg object-cover",sizes:g.toolLogo,placeholder:"blur"}):(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-lg",children:t.name.charAt(0).toUpperCase()})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:t.name}),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat((0,p.Ef)(t.pricing)),children:(0,p.mV)(t.pricing)})]})]}),(0,r.jsx)("a",{href:t.website,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-blue-600 transition-colors",children:(0,r.jsx)(i.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-2",children:t.description}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[t.tags.slice(0,3).map((e,t)=>(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:e},t)),t.tags.length>3&&(0,r.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-700",children:["+",t.tags.length-3]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:t.views})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(c.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:t.likes})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(d.A,{toolId:t._id,initialLikes:t.likes,onLoginRequired:a,onUnlike:s,isInLikedPage:l}),(0,r.jsx)(n(),{href:"/tools/".concat(t._id),className:"inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 transition-colors",children:"查看详情"})]})]})]})})}}}]);